import { NumberType } from "@oneteam/onetheme";
import { z } from "zod";

import { ConfigurationFormType } from "@src/types/FormConfiguration.ts";
import { Foundation } from "@src/types/Foundation.ts";
import { FoundationConfiguration } from "@src/types/FoundationConfiguration.ts";
import { SeriesConfig } from "@src/types/Series.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { ChartSubTypeMap, ChartType } from "../components/Chart/ChartTypes";
import { Question } from "./Question";

// Properties
export interface CommonQuestionProperties {
  required?: boolean;
  hidden?: boolean;
  disabled?: boolean;
  // alwaysAsked?: boolean;
  allowReuseAcrossForms?: boolean;
}

export interface Placeholder {
  placeholder?: string;
}

// QuestionTypes.TEXT
export type TextQuestionValue = string;

export interface TextQuestionProperties extends Placeholder {
  minLength?: number;
  maxLength?: number;
  defaultValue?: TextQuestionValue;
  isTextArea?: boolean;
  // For otai forms
  regex?: RegExp; // Regular expression for validation
  isSecret?: boolean;
}

// QuestionTypes.NUMBER
export type NumberQuestionValue = string;
export type NumberTypeStrings = `${NumberType}`;

export interface NumberQuestionProperties extends Placeholder {
  type?: NumberTypeStrings; // default is number
  decimalPlaces?: number;
  min?: number;
  max?: number;
  defaultValue?: NumberQuestionValue;
}

// QuestionTypes.SELECT
export type SelectQuestionSingleValue = string | number;
export type SelectQuestionMultipleValue = SelectQuestionSingleValue[];

export interface SelectQuestionOption {
  label?: string; // Display text
  value: SelectQuestionSingleValue;
  description?: string; // Optional description for the option
}

export enum DynamicOptionTags {
  FORM_CONFIGURATION_ID = "formConfigurationId",
  FORM_CONFIGURATION_KEY = "formConfigurationKey",
  FORM_CONFIGURATION_QUESTION_ID = "formConfigurationId.questionId",
  FOUNDATION_CONFIGURATION_ID = "foundationConfigurationId",
  FOUNDATION_COLLECTION_ID = "foundationConfigurationId.foundationCollectionId",
  FOUNDATION_COLLECTION_KEY = "foundationConfigurationId.foundationCollectionKey",
  FLOW_CONFIGURATION_ID = "flowConfigurationId",
  SERIES_CONFIGURATION_ID = "seriesConfigurationId",
  FORM_CONFIGURATION_SERIES_INTERVAL_ID = "intervalConfigurationId",
  SERIES_INTERVAL_ID = "seriesConfigurationId.intervalId",
  QUESTION_TYPES = "questionTypes"
}

export interface SelectQuestionDynamicOptions {
  tag: DynamicOptionTags;
  body?: {
    document?: WorkspaceDocument;
    parentFoundationKey?: Foundation["key"];
    parentFoundationConfigurationId?: FoundationConfiguration["id"];
    formConfigurationId?: ConfigurationFormType["id"];
    seriesConfigurationId?: SeriesConfig["id"];
    skip?: string; // "text, number, select, date, boolean, table, json, files, list"
  };
}

export interface SelectQuestionProperties extends Placeholder {
  options?: SelectQuestionOption[];
  isMultiSelect?: boolean;
  defaultValue?: SelectQuestionSingleValue | SelectQuestionMultipleValue;
  dynamicOptions?: SelectQuestionDynamicOptions;
  clearIfOptionMissing?: boolean;
}

// QuestionTypes.DATE
export type DateQuestionSingleValue = string;
export type DateQuestionRangeValue = {
  from: DateQuestionSingleValue;
  to: DateQuestionSingleValue;
};
export type DateQuestionValue =
  | DateQuestionSingleValue
  | DateQuestionRangeValue;

export interface DateQuestionProperties {
  min?: string;
  max?: string;
  isDateRange?: boolean;
  defaultValue?: DateQuestionValue;
}

// QuestionTypes.BOOLEAN
export type BooleanQuestionValue = boolean;

export interface BooleanQuestionProperties {
  trueText?: string;
  falseText?: string;
  defaultValue?: BooleanQuestionValue;
}

export enum TableStyling {
  LIST_OF_INPUTS = "listOfInputs",
  TABLE_LIST = "tableList"
}

// QuestionTypes.TABLE
export interface TableQuestionProperties {
  columns: Question[];
  styling?: TableStyling;
  charts?: Question<ChartQuestionProperties>[];
}

// Common properties for all charts
export interface CommonChartConfig<T extends ChartType = ChartType> {
  type: T;
  subType?: ChartSubTypeMap[T];
  swapColumns?: boolean;
}

// Line/Bar chart config
export interface CartesianChartConfig {
  xAxis: string[];
  series: string[];
}

// Pie chart config
export interface PieChartConfig {
  label: string[];
  value: string;
}

export type ChartConfig =
  | (CommonChartConfig<ChartType.LINE | ChartType.BAR> & CartesianChartConfig)
  | (CommonChartConfig<ChartType.PIE> & PieChartConfig);

export interface ChartQuestionProperties<
  P = CartesianChartConfig | PieChartConfig
> {
  chartConfig: CommonChartConfig & P;
}

export enum FilesFileFormats {
  audio = "audio",
  document = "document",
  image = "image",
  pdf = "pdf",
  presentation = "presentation",
  spreadsheet = "spreadsheet",
  video = "video"
}
export type FilesFileFormatsStrings = `${FilesFileFormats}`;

export interface FilesQuestionProperties {
  min?: number;
  max?: number;
  maxFileSizeMB?: number;
  restrictedFileTypes?: FilesFileFormatsStrings[];
}

// QuestionTypes.JSON
export interface JSONQuestionProperties {
  isUnstructured?: boolean;
  items: Question[];
}

export interface ListQuestionProperties {
  items: Question[];
  minLength?: number;
  maxLength?: number;
}

export type ConfigurationQuestionProperties = object;

export const commonQuestionPropertiesSchema = z.object({
  required: z.boolean().optional(),
  placeholder: z.string().optional(),
  allowReuseAcrossForms: z.boolean().optional()
});

export const textQuestionPropertiesSchema = z.object({
  minLength: z.number().optional(),
  maxLength: z.number().optional(),
  defaultValue: z.string().optional(),
  isTextArea: z.boolean().optional(),
  // For otai forms
  regex: z.string().optional(),
  isSecret: z.boolean().optional()
});

export const numberQuestionPropertiesSchema = z.object({
  type: z.nativeEnum(NumberType).optional(),
  decimalPlaces: z.number().optional(),
  min: z.number().optional(),
  max: z.number().optional(),
  defaultValue: z.string().optional()
});

export const selectQuestionSingleValueSchema: z.ZodSchema<SelectQuestionSingleValue> =
  z.lazy(() => z.union([z.string(), z.number()]));

export const selectQuestionMultipleValueSchema: z.ZodSchema<SelectQuestionMultipleValue> =
  z.lazy(() => z.array(z.union([z.string(), z.number()])));

export const selectQuestionValueSchema: z.ZodSchema<
  SelectQuestionSingleValue | SelectQuestionMultipleValue
> = z.lazy(() =>
  z.union([selectQuestionSingleValueSchema, selectQuestionMultipleValueSchema])
);

export const selectQuestionOptionsSchema: z.ZodSchema<SelectQuestionOption[]> =
  z.array(
    z.object({
      label: z.string(),
      value: z.union([z.string(), z.number()])
    })
  );

export const selectQuestionPropertiesSchema = z.object({
  options: selectQuestionOptionsSchema.optional(),
  isMultiSelect: z.boolean().optional(),
  defaultValue: selectQuestionValueSchema.optional()
});

export const dateQuestionValueSchema = z.string();

export const dateQuestionRangeValueSchema = z.object({
  from: dateQuestionValueSchema,
  to: dateQuestionValueSchema
});

export const dateQuestionPropertiesSchema = z.object({
  min: z.string().optional(),
  max: z.string().optional(),
  isDateRange: z.boolean().optional(),
  defaultValue: z
    .union([dateQuestionValueSchema, dateQuestionRangeValueSchema])
    .optional()
});

export const booleanQuestionPropertiesSchema = z.object({
  trueText: z.string().optional(),
  falseText: z.string().optional(),
  defaultValue: z.boolean().optional()
});
